'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { ArrowLeft, ArrowRight, Palette } from 'lucide-react';
import { useOnboarding } from './OnboardingContext';
import { Button } from '@/components/atoms/Button/Button';

const THEME_OPTIONS = [
  { id: 'blue', name: 'Ocean Blue', color: '#3B82F6', description: 'Professional and trustworthy' },
  { id: 'green', name: 'Forest Green', color: '#10B981', description: 'Growth and harmony' },
  { id: 'purple', name: '<PERSON> Purple', color: '#8B5CF6', description: 'Creative and inspiring' },
  { id: 'orange', name: 'Sunset Orange', color: '#F59E0B', description: 'Energetic and warm' },
  { id: 'red', name: 'Cherry Red', color: '#EF4444', description: 'Bold and passionate' },
  { id: 'teal', name: '<PERSON><PERSON>', color: '#14B8A6', description: 'Modern and fresh' },
];

export default function BrandingStep() {
  const router = useRouter();
  const { 
    state, 
    setBrandingData, 
    markStepCompleted, 
    getPreviousStep,
    getNextStep 
  } = useOnboarding();
  
  const [selectedTheme, setSelectedTheme] = useState<string>('blue');
  const [isLoading, setIsLoading] = useState(false);

  const handleBack = () => {
    const previousStep = getPreviousStep();
    if (previousStep) {
      router.push(`/onboarding/${previousStep}`);
    }
  };

  const handleNext = async () => {
    setIsLoading(true);
    
    try {
      // Store branding data
      const selectedThemeData = THEME_OPTIONS.find(theme => theme.id === selectedTheme);
      setBrandingData({
        primaryColor: selectedThemeData?.color,
        theme: selectedTheme,
      });

      // Mark step as completed
      markStepCompleted('branding');

      // Navigate to next step
      const nextStep = getNextStep();
      if (nextStep) {
        router.push(`/onboarding/${nextStep}`);
      }
    } catch (error) {
      console.error('Error saving branding data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = () => {
    // Skip branding step
    const nextStep = getNextStep();
    if (nextStep) {
      router.push(`/onboarding/${nextStep}`);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="space-y-4">
        {/* Theme Selection */}
        <div className="space-y-3">
          <h3 className="text-lg font-semibold text-gray-800 text-center">
            Select Your School Theme
          </h3>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {THEME_OPTIONS.map((theme) => (
              <motion.div
                key={theme.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`cursor-pointer p-3 rounded-lg border-2 transition-all duration-200 ${
                  selectedTheme === theme.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 bg-white'
                }`}
                onClick={() => setSelectedTheme(theme.id)}
              >
                <div className="flex flex-col items-center space-y-1">
                  <div
                    className="w-10 h-10 rounded-full shadow-md"
                    style={{ backgroundColor: theme.color }}
                  ></div>
                  <div className="text-center">
                    <div className="font-medium text-sm text-gray-800">{theme.name}</div>
                  </div>
                  {selectedTheme === theme.id && (
                    <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                      <div className="w-1.5 h-1.5 bg-white rounded-full"></div>
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between items-center pt-4 border-t border-gray-200">
          <Button
            variant="ghost"
            onClick={handleBack}
            disabled={isLoading}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-800"
          >
            <ArrowLeft size={16} />
            <span>Back</span>
          </Button>

          <div className="flex space-x-3">
            <Button
              variant="ghost"
              onClick={handleSkip}
              disabled={isLoading}
              className="text-gray-600 hover:text-gray-800"
            >
              Skip
            </Button>

            <Button
              variant="primary"
              onClick={handleNext}
              disabled={isLoading}
              className="flex items-center space-x-2"
            >
              {isLoading ? (
                <>
                  <span className="loading loading-spinner loading-sm"></span>
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <span>Continue</span>
                  <ArrowRight size={16} />
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
