'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { ArrowRight } from 'lucide-react';
import { useOnboarding } from './OnboardingContext';
import { SchoolCreationForm } from '@/components/organisms/SchoolCreationForm';
import { Button } from '@/components/atoms/Button/Button';

export default function SchoolDetailsStep() {
  const router = useRouter();
  const { 
    state, 
    setSchoolData, 
    markStepCompleted, 
    setError, 
    setLoading,
    getNextStep 
  } = useOnboarding();
  const { isLoading } = state;

  const handleSchoolCreated = () => {
    // The SchoolCreationForm component handles the school creation
    // and the success callback is called when the school is successfully created
    // We don't need to store the school data here since it's already created on the server

    // Mark step as completed
    markStepCompleted('school-details');

    // Navigate to next step
    const nextStep = getNextStep();
    if (nextStep) {
      router.push(`/onboarding/${nextStep}`);
    }
  };

  const handleSchoolCreationError = (error: string) => {
    setError(error);
  };

  const handleSkip = () => {
    // Allow skipping school creation for now, but mark as incomplete
    const nextStep = getNextStep();
    if (nextStep) {
      router.push(`/onboarding/${nextStep}`);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="space-y-6">
        {/* Instructions */}
        <div className="text-center mb-6">
          <p className="text-gray-600">
            Set up your school details to get started. This information will be used 
            to create your school profile and can be updated later.
          </p>
        </div>

        {/* School Creation Form */}
        <div className="space-y-6">
          <SchoolCreationForm
            onSuccess={handleSchoolCreated}
            onError={handleSchoolCreationError}
          />

          {/* Skip Option */}
          <div className="flex justify-between items-center pt-4 border-t border-gray-200">
            <Button
              variant="ghost"
              onClick={handleSkip}
              disabled={isLoading}
              className="text-gray-600 hover:text-gray-800"
            >
              Skip for now
            </Button>

            <div className="text-sm text-gray-500">
              You can complete this step later in your dashboard
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
