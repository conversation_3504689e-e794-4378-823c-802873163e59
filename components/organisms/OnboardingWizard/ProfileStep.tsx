'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { ArrowLeft, ArrowRight, BookOpen, Award } from 'lucide-react';
import { useOnboarding } from './OnboardingContext';
import { Button } from '@/components/atoms/Button/Button';
import { FormField } from '@/components/molecules/FormField/FormField';
import { cn } from '@/utils/cn';

const profileSchema = z.object({
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
  specialization: z.string().max(100, 'Specialization must be less than 100 characters').optional(),
  experience: z.string().max(50, 'Experience must be less than 50 characters').optional(),
});

type ProfileFormData = z.infer<typeof profileSchema>;

const SPECIALIZATION_OPTIONS = [
  'Mathematics',
  'Science',
  'English Language',
  'History',
  'Geography',
  'Art & Design',
  'Music',
  'Physical Education',
  'Computer Science',
  'Languages',
  'Other',
];

const EXPERIENCE_OPTIONS = [
  'New Teacher (0-1 years)',
  'Early Career (2-5 years)',
  'Experienced (6-10 years)',
  'Veteran (11-20 years)',
  'Expert (20+ years)',
];

export default function ProfileStep() {
  const router = useRouter();
  const { 
    setProfileData, 
    markStepCompleted, 
    getPreviousStep,
    getNextStep 
  } = useOnboarding();
  
  const [isLoading, setIsLoading] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      bio: '',
      specialization: '',
      experience: '',
    },
  });

  const watchedSpecialization = watch('specialization');
  const watchedExperience = watch('experience');

  const handleBack = () => {
    const previousStep = getPreviousStep();
    if (previousStep) {
      router.push(`/onboarding/${previousStep}`);
    }
  };

  const onSubmit = async (data: ProfileFormData) => {
    setIsLoading(true);
    
    try {
      // Store profile data
      setProfileData(data);

      // Mark step as completed
      markStepCompleted('profile');

      // Navigate to next step
      const nextStep = getNextStep();
      if (nextStep) {
        router.push(`/onboarding/${nextStep}`);
      }
    } catch (error) {
      console.error('Error saving profile data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = () => {
    // Skip profile step
    const nextStep = getNextStep();
    if (nextStep) {
      router.push(`/onboarding/${nextStep}`);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="space-y-4">
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          {/* Bio */}
          <FormField
            label="About You"
            error={errors.bio?.message}
          >
            <div className="relative">
              <div className="absolute top-3 left-3 pointer-events-none">
                <BookOpen size={18} className="text-blue-500" />
              </div>
              <textarea
                placeholder="Tell us about yourself and your teaching philosophy..."
                className={cn(
                  'w-full pl-10 pr-4 py-2 border-2 rounded-lg transition-all duration-300 focus:ring-2 focus:ring-blue-100 hover:border-blue-300 resize-none',
                  'bg-white/50 backdrop-blur-sm min-h-[80px]',
                  errors.bio
                    ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                    : 'border-gray-200 focus:border-blue-500'
                )}
                {...register('bio')}
              />
            </div>
          </FormField>

          {/* Specialization */}
          <FormField
            label="Subject Specialization"
            error={errors.specialization?.message}
          >
            <div className="space-y-2">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {SPECIALIZATION_OPTIONS.map((option) => (
                  <button
                    key={option}
                    type="button"
                    onClick={() => setValue('specialization', option)}
                    className={cn(
                      'p-2 text-sm rounded-lg border-2 transition-all duration-200 text-left',
                      watchedSpecialization === option
                        ? 'border-blue-500 bg-blue-50 text-blue-700'
                        : 'border-gray-200 hover:border-gray-300 bg-white hover:bg-gray-50'
                    )}
                  >
                    {option}
                  </button>
                ))}
              </div>
              <div className="relative">
                <input
                  type="text"
                  placeholder="Or enter your own..."
                  className={cn(
                    'w-full px-3 py-2 border-2 rounded-lg transition-all duration-300 focus:ring-2 focus:ring-blue-100 hover:border-blue-300',
                    'bg-white/50 backdrop-blur-sm',
                    errors.specialization
                      ? 'border-red-400 focus:border-red-500 focus:ring-red-100'
                      : 'border-gray-200 focus:border-blue-500'
                  )}
                  {...register('specialization')}
                />
              </div>
            </div>
          </FormField>

          {/* Experience */}
          <FormField
            label="Teaching Experience"
            error={errors.experience?.message}
          >
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              {EXPERIENCE_OPTIONS.map((option) => (
                <button
                  key={option}
                  type="button"
                  onClick={() => setValue('experience', option)}
                  className={cn(
                    'p-2 text-sm rounded-lg border-2 transition-all duration-200 text-left flex items-center space-x-2',
                    watchedExperience === option
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300 bg-white hover:bg-gray-50'
                  )}
                >
                  <Award size={14} className="text-blue-500" />
                  <span>{option}</span>
                </button>
              ))}
            </div>
          </FormField>

          {/* Navigation */}
          <div className="flex justify-between items-center pt-4 border-t border-gray-200">
            <Button
              type="button"
              variant="ghost"
              onClick={handleBack}
              disabled={isLoading}
              className="flex items-center space-x-2 text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft size={16} />
              <span>Back</span>
            </Button>

            <div className="flex space-x-3">
              <Button
                type="button"
                variant="ghost"
                onClick={handleSkip}
                disabled={isLoading}
                className="text-gray-600 hover:text-gray-800"
              >
                Skip
              </Button>

              <Button
                type="submit"
                variant="primary"
                disabled={isLoading}
                className="flex items-center space-x-2"
              >
                {isLoading ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    <span>Saving...</span>
                  </>
                ) : (
                  <>
                    <span>Continue</span>
                    <ArrowRight size={16} />
                  </>
                )}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </motion.div>
  );
}
