'use client';

import React, { useState } from 'react';
import { useForm, SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { handleCreateSchoolAction } from '@/actions/school.action';
import { createSchoolFormSchema, CreateSchoolFormData } from '@/lib/validators/school.validator';

// Props interface for the component
interface SchoolCreationFormProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const SchoolCreationForm: React.FC<SchoolCreationFormProps> = ({
  onSuccess,
  onError
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<CreateSchoolFormData>({
    resolver: zodResolver(createSchoolFormSchema),
  });

  const onSubmit: SubmitHandler<CreateSchoolFormData> = async (data) => {
    setIsSubmitting(true);
    setSuccessMessage(null);
    setErrorMessage(null);

    try {
      // Call the server action with the form data
      const result = await handleCreateSchoolAction({
        name: data.name,
        address: data.address || '',
        phoneNumber: data.phoneNumber || '',
        registeredNumber: data.registeredNumber || '', // Required by API but not in task spec
        email: data.email || '',
      });

      if (result.status === 'success') {
        setSuccessMessage('School created successfully!');
        reset(); // Reset the form on success

        // Call onSuccess callback if provided
        if (onSuccess) {
          setTimeout(() => {
            onSuccess();
          }, 1500); // Give time to show success message
        }
      } else {
        // Handle error response
        const errorMsg = Array.isArray(result.message)
          ? result.message.map((m: any) =>
              typeof m === 'object' && m.message ? m.message : String(m)
            ).join(', ')
          : String(result.message || 'Failed to create school');
        setErrorMessage(errorMsg);

        // Call onError callback if provided
        if (onError) {
          onError(errorMsg);
        }
      }
    } catch (error: any) {
      console.error('Error creating school:', error);
      const errorMsg = error.message || 'An unexpected error occurred';
      setErrorMessage(errorMsg);

      // Call onError callback if provided
      if (onError) {
        onError(errorMsg);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto">
      {/* Success Alert */}
      {successMessage && (
        <div role="alert" className="alert alert-success mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6 shrink-0 stroke-current"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span>{successMessage}</span>
        </div>
      )}

      {/* Error Alert */}
      {errorMessage && (
        <div role="alert" className="alert alert-error mb-4">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-6 w-6 shrink-0 stroke-current"
            fill="none"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
          <span>{errorMessage}</span>
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)} className="w-full">
        <fieldset className="fieldset">
          <legend className="fieldset-legend text-lg font-semibold text-gray-700 mb-4">
            School Details
          </legend>

          <div className="space-y-4">
            {/* School Name Field */}
            <div>
              <label className="label" htmlFor="name">
                <span className="text-sm font-medium text-gray-700">School Name *</span>
              </label>
              <label className={`input flex items-center gap-2 ${
                errors.name ? 'input-error' : 'focus-within:input-primary'
              }`}>
                <svg className="h-4 w-4 opacity-70" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M3 21h18"/>
                  <path d="M5 21V7l8-4v18"/>
                  <path d="M19 21V11l-6-4"/>
                </svg>
                <input
                  id="name"
                  type="text"
                  placeholder="Enter school name"
                  className="grow"
                  {...register('name')}
                  disabled={isSubmitting}
                />
              </label>
              {errors.name && (
                <p className="text-sm text-error mt-1">
                  {errors.name.message}
                </p>
              )}
            </div>

            {/* School Address Field */}
            <div>
              <label className="label" htmlFor="address">
                <span className="text-sm font-medium text-gray-700">School Address</span>
              </label>
              <label className={`input flex items-center gap-2 ${
                errors.address ? 'input-error' : 'focus-within:input-primary'
              }`}>
                <svg className="h-4 w-4 opacity-70" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                  <circle cx="12" cy="10" r="3"/>
                </svg>
                <input
                  id="address"
                  type="text"
                  placeholder="Enter school address (optional)"
                  className="grow"
                  {...register('address')}
                  disabled={isSubmitting}
                />
              </label>
              {errors.address && (
                <p className="text-sm text-error mt-1">
                  {errors.address.message}
                </p>
              )}
            </div>

            {/* School Phone Field */}
            <div>
              <label className="label" htmlFor="phoneNumber">
                <span className="text-sm font-medium text-gray-700">School Phone</span>
              </label>
              <label className={`input flex items-center gap-2 ${
                errors.phoneNumber ? 'input-error' : 'focus-within:input-primary'
              }`}>
                <svg className="h-4 w-4 opacity-70" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"/>
                </svg>
                <input
                  id="phoneNumber"
                  type="tel"
                  placeholder="Enter school phone number (optional)"
                  className="grow"
                  {...register('phoneNumber')}
                  disabled={isSubmitting}
                />
              </label>
              {errors.phoneNumber && (
                <p className="text-sm text-error mt-1">
                  {errors.phoneNumber.message}
                </p>
              )}
            </div>

            {/* School Email Field */}
            <div>
              <label className="label" htmlFor="email">
                <span className="text-sm font-medium text-gray-700">School Email</span>
              </label>
              <label className={`input flex items-center gap-2 ${
                errors.email ? 'input-error' : 'focus-within:input-primary'
              }`}>
                <svg className="h-4 w-4 opacity-70" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <rect width="20" height="16" x="2" y="4" rx="2"/>
                  <path d="m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7"/>
                </svg>
                <input
                  id="email"
                  type="email"
                  placeholder="Enter school email address (optional)"
                  className="grow"
                  {...register('email')}
                  disabled={isSubmitting}
                />
              </label>
              {errors.email && (
                <p className="text-sm text-error mt-1">
                  {errors.email.message}
                </p>
              )}
            </div>

            {/* Submit Button */}
            <div className="mt-6">
              <button
                type="submit"
                className={`btn btn-primary w-full btn-lg ${
                  isSubmitting ? 'loading' : ''
                } bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 border-0 shadow-lg`}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    Creating School...
                  </>
                ) : (
                  <>
                    <svg className="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M3 21h18"/>
                      <path d="M5 21V7l8-4v18"/>
                      <path d="M19 21V11l-6-4"/>
                    </svg>
                    Create School
                  </>
                )}
              </button>
            </div>
          </div>
        </fieldset>
      </form>
    </div>
  );
};

export default SchoolCreationForm;
