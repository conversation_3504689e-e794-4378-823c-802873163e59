'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

export interface OnboardingStep {
  id: string;
  title: string;
  description: string;
  isCompleted: boolean;
}

export interface SchoolData {
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  registeredNumber?: string;
}

export interface BrandingData {
  primaryColor?: string;
  theme?: string;
}

export interface ProfileData {
  bio?: string;
  specialization?: string;
  experience?: string;
}

export interface OnboardingState {
  currentStep: string;
  steps: OnboardingStep[];
  schoolData: SchoolData | null;
  brandingData: BrandingData | null;
  profileData: ProfileData | null;
  isLoading: boolean;
  error: string | null;
}

export interface OnboardingContextType {
  state: OnboardingState;
  setCurrentStep: (step: string) => void;
  setSchoolData: (data: SchoolData) => void;
  setBrandingData: (data: BrandingData) => void;
  setProfileData: (data: ProfileData) => void;
  markStepCompleted: (stepId: string) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  resetOnboarding: () => void;
  canNavigateToStep: (stepId: string) => boolean;
  getNextStep: () => string | null;
  getPreviousStep: () => string | null;
}

const OnboardingContext = createContext<OnboardingContextType | undefined>(undefined);

const ONBOARDING_STEPS: OnboardingStep[] = [
  {
    id: 'school-details',
    title: 'School Details',
    description: 'Set up your school information',
    isCompleted: false,
  },
  {
    id: 'branding',
    title: 'Branding',
    description: 'Customize your school appearance',
    isCompleted: false,
  },
  {
    id: 'profile',
    title: 'Profile',
    description: 'Complete your teacher profile',
    isCompleted: false,
  },
  {
    id: 'complete',
    title: 'Complete',
    description: 'Finish setup',
    isCompleted: false,
  },
];

const initialState: OnboardingState = {
  currentStep: 'school-details',
  steps: ONBOARDING_STEPS,
  schoolData: null,
  brandingData: null,
  profileData: null,
  isLoading: false,
  error: null,
};

export function OnboardingProvider({ children }: { children: ReactNode }) {
  const [state, setState] = useState<OnboardingState>(initialState);

  const setCurrentStep = (step: string) => {
    setState(prev => ({
      ...prev,
      currentStep: step,
      error: null,
    }));
  };

  const setSchoolData = (data: SchoolData) => {
    setState(prev => ({
      ...prev,
      schoolData: data,
    }));
  };

  const setBrandingData = (data: BrandingData) => {
    setState(prev => ({
      ...prev,
      brandingData: data,
    }));
  };

  const setProfileData = (data: ProfileData) => {
    setState(prev => ({
      ...prev,
      profileData: data,
    }));
  };

  const markStepCompleted = (stepId: string) => {
    setState(prev => ({
      ...prev,
      steps: prev.steps.map(step =>
        step.id === stepId ? { ...step, isCompleted: true } : step
      ),
    }));
  };

  const setLoading = (loading: boolean) => {
    setState(prev => ({
      ...prev,
      isLoading: loading,
    }));
  };

  const setError = (error: string | null) => {
    setState(prev => ({
      ...prev,
      error,
    }));
  };

  const resetOnboarding = () => {
    setState(initialState);
  };

  const canNavigateToStep = (stepId: string) => {
    const stepIndex = state.steps.findIndex(step => step.id === stepId);
    const currentStepIndex = state.steps.findIndex(step => step.id === state.currentStep);
    
    // Can navigate to current step or any previous completed step
    if (stepIndex <= currentStepIndex) {
      return true;
    }
    
    // Can navigate to next step if current step is completed
    if (stepIndex === currentStepIndex + 1) {
      const currentStep = state.steps[currentStepIndex];
      return currentStep.isCompleted;
    }
    
    return false;
  };

  const getNextStep = (): string | null => {
    const currentIndex = state.steps.findIndex(step => step.id === state.currentStep);
    if (currentIndex < state.steps.length - 1) {
      return state.steps[currentIndex + 1].id;
    }
    return null;
  };

  const getPreviousStep = (): string | null => {
    const currentIndex = state.steps.findIndex(step => step.id === state.currentStep);
    if (currentIndex > 0) {
      return state.steps[currentIndex - 1].id;
    }
    return null;
  };

  const contextValue: OnboardingContextType = {
    state,
    setCurrentStep,
    setSchoolData,
    setBrandingData,
    setProfileData,
    markStepCompleted,
    setLoading,
    setError,
    resetOnboarding,
    canNavigateToStep,
    getNextStep,
    getPreviousStep,
  };

  return (
    <OnboardingContext.Provider value={contextValue}>
      {children}
    </OnboardingContext.Provider>
  );
}

export function useOnboarding() {
  const context = useContext(OnboardingContext);
  if (context === undefined) {
    throw new Error('useOnboarding must be used within an OnboardingProvider');
  }
  return context;
}
