'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { ArrowLeft, ArrowRight, Palette } from 'lucide-react';
import { useOnboarding } from './OnboardingContext';
import { Button } from '@/components/atoms/Button/Button';

const THEME_OPTIONS = [
  { id: 'blue', name: 'Ocean Blue', color: '#3B82F6', description: 'Professional and trustworthy' },
  { id: 'green', name: 'Forest Green', color: '#10B981', description: 'Growth and harmony' },
  { id: 'purple', name: '<PERSON> Purple', color: '#8B5CF6', description: 'Creative and inspiring' },
  { id: 'orange', name: 'Sunset Orange', color: '#F59E0B', description: 'Energetic and warm' },
  { id: 'red', name: 'Cherry Red', color: '#EF4444', description: 'Bold and passionate' },
  { id: 'teal', name: '<PERSON><PERSON>', color: '#14B8A6', description: 'Modern and fresh' },
];

export default function BrandingStep() {
  const router = useRouter();
  const { 
    state, 
    setBrandingData, 
    markStepCompleted, 
    getPreviousStep,
    getNextStep 
  } = useOnboarding();
  
  const [selectedTheme, setSelectedTheme] = useState<string>('blue');
  const [isLoading, setIsLoading] = useState(false);

  const handleBack = () => {
    const previousStep = getPreviousStep();
    if (previousStep) {
      router.push(`/onboarding/${previousStep}`);
    }
  };

  const handleNext = async () => {
    setIsLoading(true);
    
    try {
      // Store branding data
      const selectedThemeData = THEME_OPTIONS.find(theme => theme.id === selectedTheme);
      setBrandingData({
        primaryColor: selectedThemeData?.color,
        theme: selectedTheme,
      });

      // Mark step as completed
      markStepCompleted('branding');

      // Navigate to next step
      const nextStep = getNextStep();
      if (nextStep) {
        router.push(`/onboarding/${nextStep}`);
      }
    } catch (error) {
      console.error('Error saving branding data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSkip = () => {
    // Skip branding step
    const nextStep = getNextStep();
    if (nextStep) {
      router.push(`/onboarding/${nextStep}`);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div className="space-y-6">
        {/* Instructions */}
        <div className="text-center mb-6">
          <div className="flex justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <Palette size={24} className="text-white" />
            </div>
          </div>
          <p className="text-gray-600">
            Choose a theme color for your school. This will be used throughout your 
            school's interface and can be changed later in settings.
          </p>
        </div>

        {/* Theme Selection */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-800 text-center">
            Select Your School Theme
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {THEME_OPTIONS.map((theme) => (
              <motion.div
                key={theme.id}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className={`cursor-pointer p-4 rounded-xl border-2 transition-all duration-200 ${
                  selectedTheme === theme.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 bg-white'
                }`}
                onClick={() => setSelectedTheme(theme.id)}
              >
                <div className="flex flex-col items-center space-y-2">
                  <div
                    className="w-12 h-12 rounded-full shadow-md"
                    style={{ backgroundColor: theme.color }}
                  ></div>
                  <div className="text-center">
                    <div className="font-medium text-gray-800">{theme.name}</div>
                    <div className="text-xs text-gray-500">{theme.description}</div>
                  </div>
                  {selectedTheme === theme.id && (
                    <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Preview */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div className="text-sm font-medium text-gray-700 mb-2">Preview:</div>
          <div className="flex items-center space-x-3">
            <div
              className="w-8 h-8 rounded-lg"
              style={{ backgroundColor: THEME_OPTIONS.find(t => t.id === selectedTheme)?.color }}
            ></div>
            <div className="text-sm text-gray-600">
              Your school interface will use this color for buttons, links, and highlights
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between items-center pt-6 border-t border-gray-200">
          <Button
            variant="ghost"
            onClick={handleBack}
            disabled={isLoading}
            className="flex items-center space-x-2 text-gray-600 hover:text-gray-800"
          >
            <ArrowLeft size={16} />
            <span>Back</span>
          </Button>

          <div className="flex space-x-3">
            <Button
              variant="ghost"
              onClick={handleSkip}
              disabled={isLoading}
              className="text-gray-600 hover:text-gray-800"
            >
              Skip
            </Button>

            <Button
              variant="primary"
              onClick={handleNext}
              disabled={isLoading}
              className="flex items-center space-x-2"
            >
              {isLoading ? (
                <>
                  <span className="loading loading-spinner loading-sm"></span>
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <span>Continue</span>
                  <ArrowRight size={16} />
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </motion.div>
  );
}
